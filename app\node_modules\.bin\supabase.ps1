#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="C:\Users\<USER>\Desktop\workspace\Rocketsourcingv2\node_modules\.pnpm\supabase@2.30.4\node_modules\supabase\bin\node_modules;C:\Users\<USER>\Desktop\workspace\Rocketsourcingv2\node_modules\.pnpm\supabase@2.30.4\node_modules\supabase\node_modules;C:\Users\<USER>\Desktop\workspace\Rocketsourcingv2\node_modules\.pnpm\supabase@2.30.4\node_modules;C:\Users\<USER>\Desktop\workspace\Rocketsourcingv2\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/c/Users/<USER>/Desktop/workspace/Rocketsourcingv2/node_modules/.pnpm/supabase@2.30.4/node_modules/supabase/bin/node_modules:/mnt/c/Users/<USER>/Desktop/workspace/Rocketsourcingv2/node_modules/.pnpm/supabase@2.30.4/node_modules/supabase/node_modules:/mnt/c/Users/<USER>/Desktop/workspace/Rocketsourcingv2/node_modules/.pnpm/supabase@2.30.4/node_modules:/mnt/c/Users/<USER>/Desktop/workspace/Rocketsourcingv2/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

# Support pipeline input
if ($MyInvocation.ExpectingInput) {
  $input | & "$basedir/../supabase/bin/supabase"   $args
} else {
  & "$basedir/../supabase/bin/supabase"   $args
}
$env:NODE_PATH=$env_node_path
exit $LASTEXITCODE
