import { APIResource } from "../../../resource.js";
import * as AuditAPI from "./audit.js";
import { Audit, AuditListParams, AuditListResponse, AuditListResponsesCursorLimitPagination } from "./audit.js";
export declare class Logs extends APIResource {
    audit: AuditAPI.Audit;
}
export declare namespace Logs {
    export { Audit as Audit, type AuditListResponse as AuditListResponse, AuditListResponsesCursorLimitPagination as AuditListResponsesCursorLimitPagination, type AuditListParams as AuditListParams, };
}
//# sourceMappingURL=logs.d.ts.map