hoistPattern:
  - '*'
hoistedDependencies:
  '@ai-sdk/gateway@1.0.12(zod@4.0.15)':
    '@ai-sdk/gateway': private
  '@ai-sdk/google@2.0.8(zod@4.0.15)':
    '@ai-sdk/google': private
  '@ai-sdk/provider-utils@3.0.5(zod@4.0.15)':
    '@ai-sdk/provider-utils': private
  '@ai-sdk/provider@2.0.0':
    '@ai-sdk/provider': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@aws-crypto/crc32@5.2.0':
    '@aws-crypto/crc32': private
  '@aws-crypto/crc32c@5.2.0':
    '@aws-crypto/crc32c': private
  '@aws-crypto/sha1-browser@5.2.0':
    '@aws-crypto/sha1-browser': private
  '@aws-crypto/sha256-browser@5.2.0':
    '@aws-crypto/sha256-browser': private
  '@aws-crypto/sha256-js@5.2.0':
    '@aws-crypto/sha256-js': private
  '@aws-crypto/supports-web-crypto@5.2.0':
    '@aws-crypto/supports-web-crypto': private
  '@aws-crypto/util@5.2.0':
    '@aws-crypto/util': private
  '@aws-sdk/client-s3@3.850.0':
    '@aws-sdk/client-s3': private
  '@aws-sdk/client-sso@3.848.0':
    '@aws-sdk/client-sso': private
  '@aws-sdk/core@3.846.0':
    '@aws-sdk/core': private
  '@aws-sdk/credential-provider-env@3.846.0':
    '@aws-sdk/credential-provider-env': private
  '@aws-sdk/credential-provider-http@3.846.0':
    '@aws-sdk/credential-provider-http': private
  '@aws-sdk/credential-provider-ini@3.848.0':
    '@aws-sdk/credential-provider-ini': private
  '@aws-sdk/credential-provider-node@3.848.0':
    '@aws-sdk/credential-provider-node': private
  '@aws-sdk/credential-provider-process@3.846.0':
    '@aws-sdk/credential-provider-process': private
  '@aws-sdk/credential-provider-sso@3.848.0':
    '@aws-sdk/credential-provider-sso': private
  '@aws-sdk/credential-provider-web-identity@3.848.0':
    '@aws-sdk/credential-provider-web-identity': private
  '@aws-sdk/middleware-bucket-endpoint@3.840.0':
    '@aws-sdk/middleware-bucket-endpoint': private
  '@aws-sdk/middleware-expect-continue@3.840.0':
    '@aws-sdk/middleware-expect-continue': private
  '@aws-sdk/middleware-flexible-checksums@3.846.0':
    '@aws-sdk/middleware-flexible-checksums': private
  '@aws-sdk/middleware-host-header@3.840.0':
    '@aws-sdk/middleware-host-header': private
  '@aws-sdk/middleware-location-constraint@3.840.0':
    '@aws-sdk/middleware-location-constraint': private
  '@aws-sdk/middleware-logger@3.840.0':
    '@aws-sdk/middleware-logger': private
  '@aws-sdk/middleware-recursion-detection@3.840.0':
    '@aws-sdk/middleware-recursion-detection': private
  '@aws-sdk/middleware-sdk-s3@3.846.0':
    '@aws-sdk/middleware-sdk-s3': private
  '@aws-sdk/middleware-ssec@3.840.0':
    '@aws-sdk/middleware-ssec': private
  '@aws-sdk/middleware-user-agent@3.848.0':
    '@aws-sdk/middleware-user-agent': private
  '@aws-sdk/nested-clients@3.848.0':
    '@aws-sdk/nested-clients': private
  '@aws-sdk/region-config-resolver@3.840.0':
    '@aws-sdk/region-config-resolver': private
  '@aws-sdk/s3-request-presigner@3.855.0':
    '@aws-sdk/s3-request-presigner': private
  '@aws-sdk/signature-v4-multi-region@3.846.0':
    '@aws-sdk/signature-v4-multi-region': private
  '@aws-sdk/token-providers@3.848.0':
    '@aws-sdk/token-providers': private
  '@aws-sdk/types@3.840.0':
    '@aws-sdk/types': private
  '@aws-sdk/util-arn-parser@3.804.0':
    '@aws-sdk/util-arn-parser': private
  '@aws-sdk/util-endpoints@3.848.0':
    '@aws-sdk/util-endpoints': private
  '@aws-sdk/util-format-url@3.840.0':
    '@aws-sdk/util-format-url': private
  '@aws-sdk/util-locate-window@3.804.0':
    '@aws-sdk/util-locate-window': private
  '@aws-sdk/util-user-agent-browser@3.840.0':
    '@aws-sdk/util-user-agent-browser': private
  '@aws-sdk/util-user-agent-node@3.848.0':
    '@aws-sdk/util-user-agent-node': private
  '@aws-sdk/xml-builder@3.821.0':
    '@aws-sdk/xml-builder': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.7':
    '@babel/compat-data': private
  '@babel/core@7.27.7':
    '@babel/core': private
  '@babel/generator@7.27.5':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.7)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.27.7':
    '@babel/parser': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/runtime-corejs3@7.27.6':
    '@babel/runtime-corejs3': private
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.7':
    '@babel/traverse': private
  '@babel/types@7.27.7':
    '@babel/types': private
  '@channel.io/channel-web-sdk-loader@2.0.0':
    '@channel.io/channel-web-sdk-loader': private
  '@clack/core@0.4.1':
    '@clack/core': private
  '@clack/prompts@0.9.1':
    '@clack/prompts': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@csstools/postcss-cascade-layers@4.0.6(postcss@8.4.49)':
    '@csstools/postcss-cascade-layers': private
  '@csstools/selector-specificity@3.1.1(postcss-selector-parser@6.1.2)':
    '@csstools/selector-specificity': private
  '@emotion/babel-plugin@11.13.5':
    '@emotion/babel-plugin': private
  '@emotion/cache@11.14.0':
    '@emotion/cache': private
  '@emotion/css@11.13.5':
    '@emotion/css': private
  '@emotion/hash@0.9.2':
    '@emotion/hash': private
  '@emotion/memoize@0.9.0':
    '@emotion/memoize': private
  '@emotion/serialize@1.3.3':
    '@emotion/serialize': private
  '@emotion/sheet@1.4.0':
    '@emotion/sheet': private
  '@emotion/unitless@0.10.0':
    '@emotion/unitless': private
  '@emotion/utils@1.4.2':
    '@emotion/utils': private
  '@emotion/weak-memoize@0.4.0':
    '@emotion/weak-memoize': private
  '@esbuild/aix-ppc64@0.21.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.21.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.21.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.21.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.21.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.21.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.21.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.21.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.21.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.21.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.21.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.21.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.21.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.21.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.21.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.21.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.21.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.6':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.21.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.6':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.21.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/openharmony-arm64@0.25.6':
    '@esbuild/openharmony-arm64': private
  '@esbuild/sunos-x64@0.21.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.21.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.21.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.21.5':
    '@esbuild/win32-x64': private
  '@floating-ui/core@1.7.2':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.2':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.4(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.10':
    '@floating-ui/utils': private
  '@google-cloud/common@6.0.0':
    '@google-cloud/common': private
  '@google-cloud/projectify@4.0.0':
    '@google-cloud/projectify': private
  '@google-cloud/promisify@5.0.0':
    '@google-cloud/promisify': private
  '@google-cloud/translate@9.1.0':
    '@google-cloud/translate': private
  '@grpc/grpc-js@1.13.4':
    '@grpc/grpc-js': private
  '@grpc/proto-loader@0.7.15':
    '@grpc/proto-loader': private
  '@img/sharp-darwin-arm64@0.34.2':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.34.2':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.1.0':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.1.0':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.1.0':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.1.0':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-ppc64@1.1.0':
    '@img/sharp-libvips-linux-ppc64': private
  '@img/sharp-libvips-linux-s390x@1.1.0':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.1.0':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.1.0':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.1.0':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.34.2':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.34.2':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.34.2':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.34.2':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.34.2':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.34.2':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.34.2':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-arm64@0.34.2':
    '@img/sharp-win32-arm64': private
  '@img/sharp-win32-ia32@0.34.2':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.34.2':
    '@img/sharp-win32-x64': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@javascript-obfuscator/escodegen@2.3.0':
    '@javascript-obfuscator/escodegen': private
  '@javascript-obfuscator/estraverse@5.4.0':
    '@javascript-obfuscator/estraverse': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.11':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@js-sdsl/ordered-map@4.4.2':
    '@js-sdsl/ordered-map': private
  '@juggle/resize-observer@3.4.0':
    '@juggle/resize-observer': private
  '@logtape/logtape@1.0.4':
    '@logtape/logtape': private
  '@minoru/react-dnd-treeview@3.5.3(dnd-core@16.0.1)(react-dnd@16.0.1(@types/node@24.0.4)(@types/react@19.1.8)(react@19.1.0))(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@minoru/react-dnd-treeview': private
  '@napi-rs/image-android-arm64@1.11.0':
    '@napi-rs/image-android-arm64': private
  '@napi-rs/image-darwin-arm64@1.11.0':
    '@napi-rs/image-darwin-arm64': private
  '@napi-rs/image-darwin-x64@1.11.0':
    '@napi-rs/image-darwin-x64': private
  '@napi-rs/image-freebsd-x64@1.11.0':
    '@napi-rs/image-freebsd-x64': private
  '@napi-rs/image-linux-arm-gnueabihf@1.11.0':
    '@napi-rs/image-linux-arm-gnueabihf': private
  '@napi-rs/image-linux-arm64-gnu@1.11.0':
    '@napi-rs/image-linux-arm64-gnu': private
  '@napi-rs/image-linux-arm64-musl@1.11.0':
    '@napi-rs/image-linux-arm64-musl': private
  '@napi-rs/image-linux-x64-gnu@1.11.0':
    '@napi-rs/image-linux-x64-gnu': private
  '@napi-rs/image-linux-x64-musl@1.11.0':
    '@napi-rs/image-linux-x64-musl': private
  '@napi-rs/image-wasm32-wasi@1.11.0':
    '@napi-rs/image-wasm32-wasi': private
  '@napi-rs/image-win32-arm64-msvc@1.11.0':
    '@napi-rs/image-win32-arm64-msvc': private
  '@napi-rs/image-win32-ia32-msvc@1.11.0':
    '@napi-rs/image-win32-ia32-msvc': private
  '@napi-rs/image-win32-x64-msvc@1.11.0':
    '@napi-rs/image-win32-x64-msvc': private
  '@napi-rs/image@1.11.0':
    '@napi-rs/image': private
  '@next/env@15.3.3':
    '@next/env': private
  '@next/swc-darwin-arm64@15.3.3':
    '@next/swc-darwin-arm64': private
  '@next/swc-darwin-x64@15.3.3':
    '@next/swc-darwin-x64': private
  '@next/swc-linux-arm64-gnu@15.3.3':
    '@next/swc-linux-arm64-gnu': private
  '@next/swc-linux-arm64-musl@15.3.3':
    '@next/swc-linux-arm64-musl': private
  '@next/swc-linux-x64-gnu@15.3.3':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@15.3.3':
    '@next/swc-linux-x64-musl': private
  '@next/swc-win32-arm64-msvc@15.3.3':
    '@next/swc-win32-arm64-msvc': private
  '@next/swc-win32-x64-msvc@15.3.3':
    '@next/swc-win32-x64-msvc': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@opentelemetry/api-logs@0.203.0':
    '@opentelemetry/api-logs': private
  '@opentelemetry/api@1.9.0':
    '@opentelemetry/api': private
  '@opentelemetry/context-async-hooks@2.0.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/context-async-hooks': private
  '@opentelemetry/core@2.0.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/core': private
  '@opentelemetry/instrumentation-amqplib@0.50.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-amqplib': private
  '@opentelemetry/instrumentation-connect@0.47.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-connect': private
  '@opentelemetry/instrumentation-dataloader@0.21.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-dataloader': private
  '@opentelemetry/instrumentation-express@0.52.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-express': private
  '@opentelemetry/instrumentation-fs@0.23.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-fs': private
  '@opentelemetry/instrumentation-generic-pool@0.47.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-generic-pool': private
  '@opentelemetry/instrumentation-graphql@0.51.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-graphql': private
  '@opentelemetry/instrumentation-hapi@0.50.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-hapi': private
  '@opentelemetry/instrumentation-http@0.203.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-http': private
  '@opentelemetry/instrumentation-ioredis@0.51.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-ioredis': private
  '@opentelemetry/instrumentation-kafkajs@0.12.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-kafkajs': private
  '@opentelemetry/instrumentation-knex@0.48.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-knex': private
  '@opentelemetry/instrumentation-koa@0.51.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-koa': private
  '@opentelemetry/instrumentation-lru-memoizer@0.48.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-lru-memoizer': private
  '@opentelemetry/instrumentation-mongodb@0.56.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mongodb': private
  '@opentelemetry/instrumentation-mongoose@0.50.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mongoose': private
  '@opentelemetry/instrumentation-mysql2@0.49.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mysql2': private
  '@opentelemetry/instrumentation-mysql@0.49.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mysql': private
  '@opentelemetry/instrumentation-pg@0.55.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-pg': private
  '@opentelemetry/instrumentation-redis@0.51.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-redis': private
  '@opentelemetry/instrumentation-tedious@0.22.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-tedious': private
  '@opentelemetry/instrumentation-undici@0.14.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-undici': private
  '@opentelemetry/instrumentation@0.203.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation': private
  '@opentelemetry/redis-common@0.38.0':
    '@opentelemetry/redis-common': private
  '@opentelemetry/resources@2.0.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/resources': private
  '@opentelemetry/sdk-trace-base@2.0.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sdk-trace-base': private
  '@opentelemetry/semantic-conventions@1.36.0':
    '@opentelemetry/semantic-conventions': private
  '@opentelemetry/sql-common@0.41.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sql-common': private
  '@pandacss/config@0.54.0':
    '@pandacss/config': private
  '@pandacss/core@0.54.0':
    '@pandacss/core': private
  '@pandacss/dev@0.54.0(typescript@5.8.3)':
    '@pandacss/dev': private
  '@pandacss/extractor@0.54.0(typescript@5.8.3)':
    '@pandacss/extractor': private
  '@pandacss/generator@0.54.0':
    '@pandacss/generator': private
  '@pandacss/is-valid-prop@0.54.0':
    '@pandacss/is-valid-prop': private
  '@pandacss/logger@0.54.0':
    '@pandacss/logger': private
  '@pandacss/node@0.54.0(typescript@5.8.3)':
    '@pandacss/node': private
  '@pandacss/parser@0.54.0(typescript@5.8.3)':
    '@pandacss/parser': private
  '@pandacss/postcss@0.54.0(typescript@5.8.3)':
    '@pandacss/postcss': private
  '@pandacss/preset-base@0.54.0':
    '@pandacss/preset-base': private
  '@pandacss/preset-panda@0.54.0':
    '@pandacss/preset-panda': private
  '@pandacss/reporter@0.54.0':
    '@pandacss/reporter': private
  '@pandacss/shared@0.54.0':
    '@pandacss/shared': private
  '@pandacss/token-dictionary@0.54.0':
    '@pandacss/token-dictionary': private
  '@pandacss/types@0.54.0':
    '@pandacss/types': private
  '@panva/hkdf@1.2.1':
    '@panva/hkdf': private
  '@prisma/instrumentation@6.13.0(@opentelemetry/api@1.9.0)':
    '@prisma/instrumentation': private
  '@protobufjs/aspromise@1.1.2':
    '@protobufjs/aspromise': private
  '@protobufjs/base64@1.1.2':
    '@protobufjs/base64': private
  '@protobufjs/codegen@2.0.4':
    '@protobufjs/codegen': private
  '@protobufjs/eventemitter@1.1.0':
    '@protobufjs/eventemitter': private
  '@protobufjs/fetch@1.1.0':
    '@protobufjs/fetch': private
  '@protobufjs/float@1.0.2':
    '@protobufjs/float': private
  '@protobufjs/inquire@1.1.0':
    '@protobufjs/inquire': private
  '@protobufjs/path@1.1.2':
    '@protobufjs/path': private
  '@protobufjs/pool@1.1.0':
    '@protobufjs/pool': private
  '@protobufjs/utf8@1.1.0':
    '@protobufjs/utf8': private
  '@radix-ui/colors@3.0.0':
    '@radix-ui/colors': private
  '@radix-ui/number@1.1.1':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.2':
    '@radix-ui/primitive': private
  '@radix-ui/react-accessible-icon@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-accessible-icon': private
  '@radix-ui/react-accordion@1.2.11(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-accordion': private
  '@radix-ui/react-alert-dialog@1.1.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-alert-dialog': private
  '@radix-ui/react-arrow@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-aspect-ratio@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-aspect-ratio': private
  '@radix-ui/react-avatar@1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-avatar': private
  '@radix-ui/react-checkbox@1.3.2(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-checkbox': private
  '@radix-ui/react-collapsible@1.1.11(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-collapsible': private
  '@radix-ui/react-collection@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.2(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context-menu@2.2.15(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-context-menu': private
  '@radix-ui/react-context@1.1.2(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-context': private
  '@radix-ui/react-dialog@1.1.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-dialog': private
  '@radix-ui/react-direction@1.1.1(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-dropdown-menu@2.1.15(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-dropdown-menu': private
  '@radix-ui/react-focus-guards@1.1.2(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-form@0.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-form': private
  '@radix-ui/react-hover-card@1.1.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-hover-card': private
  '@radix-ui/react-id@1.1.1(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-id': private
  '@radix-ui/react-label@2.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-label': private
  '@radix-ui/react-menu@2.1.15(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-menubar@1.1.15(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-menubar': private
  '@radix-ui/react-navigation-menu@1.2.13(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-navigation-menu': private
  '@radix-ui/react-one-time-password-field@0.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-one-time-password-field': private
  '@radix-ui/react-password-toggle-field@0.1.2(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-password-toggle-field': private
  '@radix-ui/react-popover@1.1.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-popover': private
  '@radix-ui/react-popper@1.2.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.9(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.4(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-progress@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-progress': private
  '@radix-ui/react-radio-group@1.3.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-radio-group': private
  '@radix-ui/react-roving-focus@1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-scroll-area@1.2.9(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-scroll-area': private
  '@radix-ui/react-select@2.2.5(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-select': private
  '@radix-ui/react-separator@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-separator': private
  '@radix-ui/react-slider@1.3.5(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-slider': private
  '@radix-ui/react-slot@1.2.3(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-slot': private
  '@radix-ui/react-switch@1.2.5(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-switch': private
  '@radix-ui/react-tabs@1.1.12(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-tabs': private
  '@radix-ui/react-toast@1.2.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-toast': private
  '@radix-ui/react-toggle-group@1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-toggle-group': private
  '@radix-ui/react-toggle@1.1.9(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-toggle': private
  '@radix-ui/react-toolbar@1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-toolbar': private
  '@radix-ui/react-tooltip@1.2.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-tooltip': private
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-effect-event@0.0.2(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-use-effect-event': private
  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-is-hydrated@0.1.0(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-use-is-hydrated': private
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.1(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.1(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.1(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.2.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.1':
    '@radix-ui/rect': private
  '@radix-ui/themes@3.2.1(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/themes': private
  '@react-dnd/asap@5.0.2':
    '@react-dnd/asap': private
  '@react-dnd/invariant@4.0.2':
    '@react-dnd/invariant': private
  '@react-dnd/shallowequal@4.0.2':
    '@react-dnd/shallowequal': private
  '@reduxjs/toolkit@2.8.2(react-redux@9.2.0(@types/react@19.1.8)(react@19.1.0)(redux@5.0.1))(react@19.1.0)':
    '@reduxjs/toolkit': private
  '@rolldown/pluginutils@1.0.0-beta.19':
    '@rolldown/pluginutils': private
  '@rollup/plugin-commonjs@28.0.1(rollup@4.44.1)':
    '@rollup/plugin-commonjs': private
  '@rollup/pluginutils@5.2.0(rollup@4.44.1)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.44.1':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.44.1':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.44.1':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.44.1':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.44.1':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.44.1':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.44.1':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.44.1':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.44.1':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.44.1':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.44.1':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.44.1':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.44.1':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.44.1':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.44.1':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.44.1':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.44.1':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.44.1':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.44.1':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.44.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@sentry-internal/browser-utils@10.5.0':
    '@sentry-internal/browser-utils': private
  '@sentry-internal/feedback@10.5.0':
    '@sentry-internal/feedback': private
  '@sentry-internal/replay-canvas@10.5.0':
    '@sentry-internal/replay-canvas': private
  '@sentry-internal/replay@10.5.0':
    '@sentry-internal/replay': private
  '@sentry/babel-plugin-component-annotate@4.1.1':
    '@sentry/babel-plugin-component-annotate': private
  '@sentry/browser@10.5.0':
    '@sentry/browser': private
  '@sentry/bundler-plugin-core@4.1.1':
    '@sentry/bundler-plugin-core': private
  '@sentry/cli-darwin@2.52.0':
    '@sentry/cli-darwin': private
  '@sentry/cli-linux-arm64@2.52.0':
    '@sentry/cli-linux-arm64': private
  '@sentry/cli-linux-arm@2.52.0':
    '@sentry/cli-linux-arm': private
  '@sentry/cli-linux-i686@2.52.0':
    '@sentry/cli-linux-i686': private
  '@sentry/cli-linux-x64@2.52.0':
    '@sentry/cli-linux-x64': private
  '@sentry/cli-win32-arm64@2.52.0':
    '@sentry/cli-win32-arm64': private
  '@sentry/cli-win32-i686@2.52.0':
    '@sentry/cli-win32-i686': private
  '@sentry/cli-win32-x64@2.52.0':
    '@sentry/cli-win32-x64': private
  '@sentry/cli@2.52.0':
    '@sentry/cli': private
  '@sentry/core@10.5.0':
    '@sentry/core': private
  '@sentry/nextjs@10.5.0(@opentelemetry/context-async-hooks@2.0.1(@opentelemetry/api@1.9.0))(@opentelemetry/core@2.0.1(@opentelemetry/api@1.9.0))(@opentelemetry/sdk-trace-base@2.0.1(@opentelemetry/api@1.9.0))(next@15.3.3(@opentelemetry/api@1.9.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(react@19.1.0)(webpack@5.101.3)':
    '@sentry/nextjs': private
  '@sentry/node-core@10.5.0(@opentelemetry/api@1.9.0)(@opentelemetry/context-async-hooks@2.0.1(@opentelemetry/api@1.9.0))(@opentelemetry/core@2.0.1(@opentelemetry/api@1.9.0))(@opentelemetry/instrumentation@0.203.0(@opentelemetry/api@1.9.0))(@opentelemetry/resources@2.0.1(@opentelemetry/api@1.9.0))(@opentelemetry/sdk-trace-base@2.0.1(@opentelemetry/api@1.9.0))(@opentelemetry/semantic-conventions@1.36.0)':
    '@sentry/node-core': private
  '@sentry/node@10.5.0':
    '@sentry/node': private
  '@sentry/opentelemetry@10.5.0(@opentelemetry/api@1.9.0)(@opentelemetry/context-async-hooks@2.0.1(@opentelemetry/api@1.9.0))(@opentelemetry/core@2.0.1(@opentelemetry/api@1.9.0))(@opentelemetry/sdk-trace-base@2.0.1(@opentelemetry/api@1.9.0))(@opentelemetry/semantic-conventions@1.36.0)':
    '@sentry/opentelemetry': private
  '@sentry/react@10.5.0(react@19.1.0)':
    '@sentry/react': private
  '@sentry/vercel-edge@10.5.0':
    '@sentry/vercel-edge': private
  '@sentry/webpack-plugin@4.1.1(webpack@5.101.3)':
    '@sentry/webpack-plugin': private
  '@shuding/opentype.js@1.4.0-beta.0':
    '@shuding/opentype.js': private
  '@smithy/abort-controller@4.0.4':
    '@smithy/abort-controller': private
  '@smithy/chunked-blob-reader-native@4.0.0':
    '@smithy/chunked-blob-reader-native': private
  '@smithy/chunked-blob-reader@5.0.0':
    '@smithy/chunked-blob-reader': private
  '@smithy/config-resolver@4.1.4':
    '@smithy/config-resolver': private
  '@smithy/core@3.7.2':
    '@smithy/core': private
  '@smithy/credential-provider-imds@4.0.6':
    '@smithy/credential-provider-imds': private
  '@smithy/eventstream-codec@4.0.4':
    '@smithy/eventstream-codec': private
  '@smithy/eventstream-serde-browser@4.0.4':
    '@smithy/eventstream-serde-browser': private
  '@smithy/eventstream-serde-config-resolver@4.1.2':
    '@smithy/eventstream-serde-config-resolver': private
  '@smithy/eventstream-serde-node@4.0.4':
    '@smithy/eventstream-serde-node': private
  '@smithy/eventstream-serde-universal@4.0.4':
    '@smithy/eventstream-serde-universal': private
  '@smithy/fetch-http-handler@5.1.0':
    '@smithy/fetch-http-handler': private
  '@smithy/hash-blob-browser@4.0.4':
    '@smithy/hash-blob-browser': private
  '@smithy/hash-node@4.0.4':
    '@smithy/hash-node': private
  '@smithy/hash-stream-node@4.0.4':
    '@smithy/hash-stream-node': private
  '@smithy/invalid-dependency@4.0.4':
    '@smithy/invalid-dependency': private
  '@smithy/is-array-buffer@4.0.0':
    '@smithy/is-array-buffer': private
  '@smithy/md5-js@4.0.4':
    '@smithy/md5-js': private
  '@smithy/middleware-content-length@4.0.4':
    '@smithy/middleware-content-length': private
  '@smithy/middleware-endpoint@4.1.17':
    '@smithy/middleware-endpoint': private
  '@smithy/middleware-retry@4.1.18':
    '@smithy/middleware-retry': private
  '@smithy/middleware-serde@4.0.8':
    '@smithy/middleware-serde': private
  '@smithy/middleware-stack@4.0.4':
    '@smithy/middleware-stack': private
  '@smithy/node-config-provider@4.1.3':
    '@smithy/node-config-provider': private
  '@smithy/node-http-handler@4.1.0':
    '@smithy/node-http-handler': private
  '@smithy/property-provider@4.0.4':
    '@smithy/property-provider': private
  '@smithy/protocol-http@5.1.2':
    '@smithy/protocol-http': private
  '@smithy/querystring-builder@4.0.4':
    '@smithy/querystring-builder': private
  '@smithy/querystring-parser@4.0.4':
    '@smithy/querystring-parser': private
  '@smithy/service-error-classification@4.0.6':
    '@smithy/service-error-classification': private
  '@smithy/shared-ini-file-loader@4.0.4':
    '@smithy/shared-ini-file-loader': private
  '@smithy/signature-v4@5.1.2':
    '@smithy/signature-v4': private
  '@smithy/smithy-client@4.4.9':
    '@smithy/smithy-client': private
  '@smithy/types@4.3.1':
    '@smithy/types': private
  '@smithy/url-parser@4.0.4':
    '@smithy/url-parser': private
  '@smithy/util-base64@4.0.0':
    '@smithy/util-base64': private
  '@smithy/util-body-length-browser@4.0.0':
    '@smithy/util-body-length-browser': private
  '@smithy/util-body-length-node@4.0.0':
    '@smithy/util-body-length-node': private
  '@smithy/util-buffer-from@4.0.0':
    '@smithy/util-buffer-from': private
  '@smithy/util-config-provider@4.0.0':
    '@smithy/util-config-provider': private
  '@smithy/util-defaults-mode-browser@4.0.25':
    '@smithy/util-defaults-mode-browser': private
  '@smithy/util-defaults-mode-node@4.0.25':
    '@smithy/util-defaults-mode-node': private
  '@smithy/util-endpoints@3.0.6':
    '@smithy/util-endpoints': private
  '@smithy/util-hex-encoding@4.0.0':
    '@smithy/util-hex-encoding': private
  '@smithy/util-middleware@4.0.4':
    '@smithy/util-middleware': private
  '@smithy/util-retry@4.0.6':
    '@smithy/util-retry': private
  '@smithy/util-stream@4.2.3':
    '@smithy/util-stream': private
  '@smithy/util-uri-escape@4.0.0':
    '@smithy/util-uri-escape': private
  '@smithy/util-utf8@4.0.0':
    '@smithy/util-utf8': private
  '@smithy/util-waiter@4.0.6':
    '@smithy/util-waiter': private
  '@standard-schema/spec@1.0.0':
    '@standard-schema/spec': private
  '@standard-schema/utils@0.3.0':
    '@standard-schema/utils': private
  '@supabase/auth-js@2.70.0':
    '@supabase/auth-js': private
  '@supabase/functions-js@2.4.5':
    '@supabase/functions-js': private
  '@supabase/node-fetch@2.6.15':
    '@supabase/node-fetch': private
  '@supabase/postgrest-js@1.19.4':
    '@supabase/postgrest-js': private
  '@supabase/realtime-js@2.11.15':
    '@supabase/realtime-js': private
  '@supabase/ssr@0.6.1(@supabase/supabase-js@2.50.3)':
    '@supabase/ssr': private
  '@supabase/storage-js@2.7.1':
    '@supabase/storage-js': private
  '@supabase/supabase-js@2.50.3':
    '@supabase/supabase-js': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@tanstack/query-core@5.81.5':
    '@tanstack/query-core': private
  '@tanstack/query-devtools@5.81.2':
    '@tanstack/query-devtools': private
  '@tanstack/react-query-devtools@5.81.5(@tanstack/react-query@5.81.5(react@19.1.0))(react@19.1.0)':
    '@tanstack/react-query-devtools': private
  '@tanstack/react-query@5.81.5(react@19.1.0)':
    '@tanstack/react-query': private
  '@tootallnate/once@2.0.0':
    '@tootallnate/once': private
  '@tootallnate/quickjs-emscripten@0.23.0':
    '@tootallnate/quickjs-emscripten': private
  '@ts-morph/common@0.25.0':
    '@ts-morph/common': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@turbo/workspaces@2.5.4':
    '@turbo/workspaces': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/caseless@0.12.5':
    '@types/caseless': private
  '@types/chrome@0.0.326':
    '@types/chrome': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/d3-array@3.2.1':
    '@types/d3-array': private
  '@types/d3-color@3.1.3':
    '@types/d3-color': private
  '@types/d3-ease@3.0.2':
    '@types/d3-ease': private
  '@types/d3-interpolate@3.0.4':
    '@types/d3-interpolate': private
  '@types/d3-path@3.1.1':
    '@types/d3-path': private
  '@types/d3-scale@4.0.9':
    '@types/d3-scale': private
  '@types/d3-shape@3.1.7':
    '@types/d3-shape': private
  '@types/d3-time@3.0.4':
    '@types/d3-time': private
  '@types/d3-timer@3.0.2':
    '@types/d3-timer': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': public
  '@types/eslint@9.6.1':
    '@types/eslint': public
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/filesystem@0.0.36':
    '@types/filesystem': private
  '@types/filewriter@0.0.33':
    '@types/filewriter': private
  '@types/glob@7.2.0':
    '@types/glob': private
  '@types/har-format@1.2.16':
    '@types/har-format': private
  '@types/inquirer@6.5.0':
    '@types/inquirer': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/minimatch@3.0.5':
    '@types/minimatch': private
  '@types/mysql@2.15.27':
    '@types/mysql': private
  '@types/node-fetch@2.6.12':
    '@types/node-fetch': private
  '@types/node@24.0.4':
    '@types/node': private
  '@types/parse-json@4.0.2':
    '@types/parse-json': private
  '@types/pg-pool@2.0.6':
    '@types/pg-pool': private
  '@types/pg@8.15.4':
    '@types/pg': private
  '@types/phoenix@1.6.6':
    '@types/phoenix': private
  '@types/prop-types@15.7.15':
    '@types/prop-types': private
  '@types/react-dom@19.1.6(@types/react@19.1.8)':
    '@types/react-dom': private
  '@types/react@19.1.8':
    '@types/react': private
  '@types/request@2.48.12':
    '@types/request': private
  '@types/shimmer@1.2.0':
    '@types/shimmer': private
  '@types/tedious@4.0.14':
    '@types/tedious': private
  '@types/through@0.0.33':
    '@types/through': private
  '@types/tinycolor2@1.4.6':
    '@types/tinycolor2': private
  '@types/tough-cookie@4.0.5':
    '@types/tough-cookie': private
  '@types/use-sync-external-store@0.0.6':
    '@types/use-sync-external-store': private
  '@types/uuid@9.0.8':
    '@types/uuid': private
  '@types/validator@13.15.2':
    '@types/validator': private
  '@types/ws@8.18.1':
    '@types/ws': private
  '@vitejs/plugin-react@4.6.0(vite@5.4.19(@types/node@24.0.4)(lightningcss@1.30.1)(terser@5.43.1))':
    '@vitejs/plugin-react': private
  '@vue/compiler-core@3.4.19':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.4.19':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.4.19':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.4.19':
    '@vue/compiler-ssr': private
  '@vue/shared@3.4.19':
    '@vue/shared': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  abort-controller@3.0.0:
    abort-controller: private
  acorn-import-attributes@1.9.5(acorn@8.15.0):
    acorn-import-attributes: private
  acorn-import-phases@1.0.4(acorn@8.15.0):
    acorn-import-phases: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.8.2:
    acorn: private
  agent-base@7.1.4:
    agent-base: private
  agentkeepalive@4.6.0:
    agentkeepalive: private
  aggregate-error@3.1.0:
    aggregate-error: private
  ai@5.0.23(zod@4.0.15):
    ai: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@5.1.0(ajv@8.17.1):
    ajv-keywords: private
  ajv@8.17.1:
    ajv: private
  ansi-colors@4.1.3:
    ansi-colors: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  app:
    '@rocketsourcing/app': private
  arg@4.1.3:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.6:
    aria-hidden: private
  array-differ@3.0.0:
    array-differ: private
  array-union@2.1.0:
    array-union: private
  arrify@2.0.1:
    arrify: private
  assert@2.0.0:
    assert: private
  ast-types@0.13.4:
    ast-types: private
  astral-regex@2.0.0:
    astral-regex: private
  asynckit@0.4.0:
    asynckit: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  babel-plugin-macros@3.1.0:
    babel-plugin-macros: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-arraybuffer@1.0.2:
    base64-arraybuffer: private
  base64-js@0.0.8:
    base64-js: private
  basic-ftp@5.0.5:
    basic-ftp: private
  bignumber.js@9.3.0:
    bignumber.js: private
  bin-links@5.0.0:
    bin-links: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bl@4.1.0:
    bl: private
  bowser@2.11.0:
    bowser: private
  brace-expansion@2.0.2:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.23.3:
    browserslist: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@6.0.3:
    buffer: private
  bundle-n-require@1.1.2:
    bundle-n-require: private
  busboy@1.6.0:
    busboy: private
  cac@6.7.14:
    cac: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camel-case@3.0.0:
    camel-case: private
  camelize@1.0.1:
    camelize: private
  caniuse-api@3.0.0:
    caniuse-api: private
  caniuse-lite@1.0.30001727:
    caniuse-lite: private
  chalk@3.0.0:
    chalk: private
  chance@1.1.9:
    chance: private
  change-case@3.1.0:
    change-case: private
  char-regex@1.0.2:
    char-regex: private
  chardet@0.7.0:
    chardet: private
  charenc@0.0.2:
    charenc: private
  chokidar@4.0.3:
    chokidar: private
  chownr@3.0.0:
    chownr: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: private
  class-validator@0.14.1:
    class-validator: private
  classnames@2.5.1:
    classnames: private
  clean-stack@2.2.0:
    clean-stack: private
  cli-cursor@3.1.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  cli-width@3.0.0:
    cli-width: private
  client-only@0.0.1:
    client-only: private
  cliui@8.0.1:
    cliui: private
  clone@1.0.4:
    clone: private
  cloudflare@4.4.1:
    cloudflare: private
  clsx@2.1.1:
    clsx: private
  cmd-shim@7.0.0:
    cmd-shim: private
  code-block-writer@13.0.3:
    code-block-writer: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@10.0.1:
    commander: private
  commondir@1.0.1:
    commondir: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.1.8:
    confbox: private
  constant-case@2.0.0:
    constant-case: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie@1.0.2:
    cookie: private
  core-js-pure@3.43.0:
    core-js-pure: private
  cosmiconfig@7.1.0:
    cosmiconfig: private
  create-require@1.1.1:
    create-require: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crosspath@2.0.0:
    crosspath: private
  crypt@0.0.2:
    crypt: private
  css-background-parser@0.1.0:
    css-background-parser: private
  css-box-shadow@1.0.0-3:
    css-box-shadow: private
  css-color-keywords@1.0.0:
    css-color-keywords: private
  css-gradient-parser@0.0.16:
    css-gradient-parser: private
  css-line-break@2.1.0:
    css-line-break: private
  css-to-react-native@3.2.0:
    css-to-react-native: private
  cssesc@3.0.0:
    cssesc: private
  cssnano-utils@5.0.1(postcss@8.4.49):
    cssnano-utils: private
  csstype@3.1.3:
    csstype: private
  d3-array@3.2.4:
    d3-array: private
  d3-color@3.1.0:
    d3-color: private
  d3-ease@3.0.1:
    d3-ease: private
  d3-format@3.1.0:
    d3-format: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-path@3.1.0:
    d3-path: private
  d3-scale@4.0.2:
    d3-scale: private
  d3-shape@3.2.0:
    d3-shape: private
  d3-time-format@4.1.0:
    d3-time-format: private
  d3-time@3.1.0:
    d3-time: private
  d3-timer@3.0.1:
    d3-timer: private
  data-uri-to-buffer@4.0.1:
    data-uri-to-buffer: private
  date-fns@4.1.0:
    date-fns: private
  debug@4.4.1:
    debug: private
  decimal.js-light@2.5.1:
    decimal.js-light: private
  deep-extend@0.6.0:
    deep-extend: private
  deep-is@0.1.4:
    deep-is: private
  defaults@1.0.4:
    defaults: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  degenerator@5.0.1:
    degenerator: private
  del@5.1.0:
    del: private
  delayed-stream@1.0.0:
    delayed-stream: private
  detect-libc@2.0.4:
    detect-libc: private
  detect-node-es@1.1.0:
    detect-node-es: private
  diff@4.0.2:
    diff: private
  dir-glob@3.0.1:
    dir-glob: private
  dnd-core@16.0.1:
    dnd-core: private
  dnd-multi-backend@8.1.2(dnd-core@16.0.1):
    dnd-multi-backend: private
  dot-case@2.1.1:
    dot-case: private
  dotenv@16.6.1:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexify@4.1.3:
    duplexify: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  electron-to-chromium@1.5.180:
    electron-to-chromium: private
  emoji-regex-xs@2.0.1:
    emoji-regex-xs: private
  emoji-regex@8.0.0:
    emoji-regex: private
  end-of-stream@1.4.5:
    end-of-stream: private
  enhanced-resolve@5.18.3:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  error-ex@1.3.2:
    error-ex: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-toolkit@1.39.7:
    es-toolkit: private
  es6-object-assign@1.1.0:
    es6-object-assign: private
  esbuild@0.21.5:
    esbuild: private
  escalade@3.1.2:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  eslint-scope@7.1.1:
    eslint-scope: public
  eslint-visitor-keys@3.3.0:
    eslint-visitor-keys: public
  esprima@4.0.1:
    esprima: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  event-target-shim@5.0.1:
    event-target-shim: private
  eventemitter3@5.0.1:
    eventemitter3: private
  events@3.3.0:
    events: private
  eventsource-parser@3.0.3:
    eventsource-parser: private
  execa@5.1.1:
    execa: private
  extend@3.0.2:
    extend: private
  extension:
    '@rocketsourcing/extension': private
  external-editor@3.1.0:
    external-editor: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-uri@3.0.6:
    fast-uri: private
  fast-xml-parser@5.2.5:
    fast-xml-parser: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  fetch-blob@3.2.0:
    fetch-blob: private
  fflate@0.7.4:
    fflate: private
  figures@3.2.0:
    figures: private
  fill-range@7.1.1:
    fill-range: private
  find-root@1.1.0:
    find-root: private
  find-up@5.0.0:
    find-up: private
  for-each@0.3.5:
    for-each: private
  form-data-encoder@1.7.2:
    form-data-encoder: private
  form-data@4.0.3:
    form-data: private
  formdata-node@4.4.1:
    formdata-node: private
  formdata-polyfill@4.0.10:
    formdata-polyfill: private
  forwarded-parse@2.1.2:
    forwarded-parse: private
  framer-motion@11.18.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    framer-motion: private
  fs-extra@10.1.0:
    fs-extra: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gaxios@7.1.1:
    gaxios: private
  gcp-metadata@7.0.1:
    gcp-metadata: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@6.0.1:
    get-stream: private
  get-uri@6.0.4:
    get-uri: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@9.3.5:
    glob: private
  globals@11.12.0:
    globals: private
  globby@10.0.2:
    globby: private
  google-auth-library@10.1.0:
    google-auth-library: private
  google-gax@5.0.1:
    google-gax: private
  google-logging-utils@1.1.1:
    google-logging-utils: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  gradient-string@2.0.2:
    gradient-string: private
  gtoken@8.0.0:
    gtoken: private
  handlebars@4.7.8:
    handlebars: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  header-case@1.0.1:
    header-case: private
  hex-rgb@4.3.0:
    hex-rgb: private
  hoist-non-react-statics@3.3.2:
    hoist-non-react-statics: private
  hookable@5.5.3:
    hookable: private
  html-entities@2.6.0:
    html-entities: private
  html-tags@3.3.1:
    html-tags: private
  html2canvas-pro@1.5.11:
    html2canvas-pro: private
  html2canvas@1.4.1:
    html2canvas: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  human-signals@2.1.0:
    human-signals: private
  humanize-ms@1.2.1:
    humanize-ms: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  immer@10.1.1:
    immer: private
  import-fresh@3.3.1:
    import-fresh: private
  import-in-the-middle@1.14.2:
    import-in-the-middle: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  inquirer@8.2.6:
    inquirer: private
  internmap@2.0.3:
    internmap: private
  inversify@6.0.1:
    inversify: private
  ip-address@9.0.5:
    ip-address: private
  is-arguments@1.2.0:
    is-arguments: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-buffer@1.1.6:
    is-buffer: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-html@2.0.0:
    is-html: private
  is-interactive@1.0.0:
    is-interactive: private
  is-lower-case@1.1.3:
    is-lower-case: private
  is-nan@1.3.2:
    is-nan: private
  is-number@7.0.0:
    is-number: private
  is-path-cwd@2.2.0:
    is-path-cwd: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-reference@1.2.1:
    is-reference: private
  is-regex@1.2.1:
    is-regex: private
  is-stream@2.0.1:
    is-stream: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-unicode-supported@0.1.0:
    is-unicode-supported: private
  is-upper-case@1.1.2:
    is-upper-case: private
  is-what@4.1.16:
    is-what: private
  isbinaryfile@4.0.10:
    isbinaryfile: private
  isexe@2.0.0:
    isexe: private
  isows@1.0.7(ws@8.18.3):
    isows: private
  javascript-obfuscator@4.1.1:
    javascript-obfuscator: private
  javascript-stringify@2.1.0:
    javascript-stringify: private
  jest-worker@27.5.1:
    jest-worker: private
  jose@6.0.11:
    jose: private
  js-string-escape@1.0.1:
    js-string-escape: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbn@1.1.0:
    jsbn: private
  jsesc@3.1.0:
    jsesc: private
  json-bigint@1.0.0:
    json-bigint: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@1.0.0:
    json-schema-traverse: private
  json-schema@0.4.0:
    json-schema: private
  json5@2.2.3:
    json5: private
  jsonc-parser@3.3.1:
    jsonc-parser: private
  jsonfile@6.1.0:
    jsonfile: private
  jwa@2.0.1:
    jwa: private
  jws@4.0.0:
    jws: private
  jwt-decode@4.0.0:
    jwt-decode: private
  kleur@4.1.5:
    kleur: private
  levn@0.3.0:
    levn: private
  libphonenumber-js@1.12.9:
    libphonenumber-js: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  linebreak@1.1.0:
    linebreak: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  loader-runner@4.3.0:
    loader-runner: private
  locate-path@6.0.0:
    locate-path: private
  lodash.camelcase@4.3.0:
    lodash.camelcase: private
  lodash.get@4.4.2:
    lodash.get: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.throttle@4.1.1:
    lodash.throttle: private
  lodash.truncate@4.4.2:
    lodash.truncate: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  lodash@4.17.21:
    lodash: private
  log-symbols@3.0.0:
    log-symbols: private
  long@5.3.2:
    long: private
  look-it-up@2.1.0:
    look-it-up: private
  loose-envify@1.4.0:
    loose-envify: private
  lower-case-first@1.0.2:
    lower-case-first: private
  lower-case@1.1.4:
    lower-case: private
  lru-cache@6.0.0:
    lru-cache: private
  lucide-react@0.523.0(react@19.1.0):
    lucide-react: private
  magic-string@0.30.17:
    magic-string: private
  make-error@1.3.6:
    make-error: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  md5@2.3.0:
    md5: private
  merge-anything@5.1.7:
    merge-anything: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  microdiff@1.3.2:
    microdiff: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mimic-fn@2.1.0:
    mimic-fn: private
  minimatch@9.0.5:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@2.1.3:
    mkdirp: private
  mlly@1.7.4:
    mlly: private
  module-details-from-path@1.0.4:
    module-details-from-path: private
  motion-dom@11.18.1:
    motion-dom: private
  motion-utils@11.18.1:
    motion-utils: private
  ms@2.1.3:
    ms: private
  multimatch@5.0.0:
    multimatch: private
  mute-stream@0.0.8:
    mute-stream: private
  nanoid@5.1.5:
    nanoid: private
  neo-async@2.6.2:
    neo-async: private
  netmask@2.0.2:
    netmask: private
  next-auth@4.24.11(next@15.3.3(@opentelemetry/api@1.9.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    next-auth: private
  next@15.3.3(@opentelemetry/api@1.9.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    next: private
  no-case@2.3.2:
    no-case: private
  node-domexception@1.0.0:
    node-domexception: private
  node-eval@2.0.0:
    node-eval: private
  node-fetch@2.7.0:
    node-fetch: private
  node-plop@0.26.3:
    node-plop: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-normalize-package-bin@4.0.0:
    npm-normalize-package-bin: private
  npm-run-path@4.0.1:
    npm-run-path: private
  oauth@0.9.15:
    oauth: private
  object-hash@3.0.0:
    object-hash: private
  object-is@1.1.6:
    object-is: private
  object-keys@1.1.1:
    object-keys: private
  object-path@0.11.8:
    object-path: private
  oidc-token-hash@5.1.0:
    oidc-token-hash: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  opencollective-postinstall@2.0.3:
    opencollective-postinstall: private
  openid-client@5.7.1:
    openid-client: private
  optionator@0.8.3:
    optionator: private
  ora@4.1.1:
    ora: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  outdent@0.8.0:
    outdent: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@3.0.0:
    p-map: private
  pac-proxy-agent@7.2.0:
    pac-proxy-agent: private
  pac-resolver@7.0.1:
    pac-resolver: private
  package-manager-detector@0.1.0:
    package-manager-detector: private
  pako@0.2.9:
    pako: private
  param-case@2.1.1:
    param-case: private
  parent-module@1.0.1:
    parent-module: private
  parse-css-color@0.2.1:
    parse-css-color: private
  parse-json@5.2.0:
    parse-json: private
  pascal-case@2.0.1:
    pascal-case: private
  path-browserify@1.0.1:
    path-browserify: private
  path-case@2.1.1:
    path-case: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-type@4.0.0:
    path-type: private
  pathe@1.1.2:
    pathe: private
  perfect-debounce@1.0.0:
    perfect-debounce: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-protocol@1.10.3:
    pg-protocol: private
  pg-types@2.2.0:
    pg-types: private
  picocolors@1.0.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pkg-types@1.0.3:
    pkg-types: private
  pluralize@8.0.0:
    pluralize: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-discard-duplicates@7.0.1(postcss@8.4.49):
    postcss-discard-duplicates: private
  postcss-discard-empty@7.0.0(postcss@8.4.49):
    postcss-discard-empty: private
  postcss-merge-rules@7.0.4(postcss@8.4.49):
    postcss-merge-rules: private
  postcss-minify-selectors@7.0.4(postcss@8.4.49):
    postcss-minify-selectors: private
  postcss-nested@6.0.1(postcss@8.4.49):
    postcss-nested: private
  postcss-normalize-whitespace@7.0.0(postcss@8.4.49):
    postcss-normalize-whitespace: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.5.6:
    postcss: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  preact-render-to-string@5.2.6(preact@10.26.9):
    preact-render-to-string: private
  preact@10.26.9:
    preact: private
  prelude-ls@1.1.2:
    prelude-ls: private
  prettier@3.2.5:
    prettier: public
  pretty-format@3.8.0:
    pretty-format: private
  proc-log@5.0.0:
    proc-log: private
  process@0.11.10:
    process: private
  progress@2.0.3:
    progress: private
  proto3-json-serializer@3.0.1:
    proto3-json-serializer: private
  protobufjs@7.5.3:
    protobufjs: private
  proxy-agent@6.5.0:
    proxy-agent: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  queue-microtask@1.2.3:
    queue-microtask: private
  radix-ui@1.4.2(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    radix-ui: private
  randombytes@2.1.0:
    randombytes: private
  rc@1.2.8:
    rc: private
  react-best-gradient-color-picker@3.0.14(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    react-best-gradient-color-picker: private
  react-dnd-html5-backend@16.0.1:
    react-dnd-html5-backend: private
  react-dnd-touch-backend@16.0.1:
    react-dnd-touch-backend: private
  react-dnd@16.0.1(@types/node@24.0.4)(@types/react@19.1.8)(react@19.1.0):
    react-dnd: private
  react-dom@19.1.0(react@19.1.0):
    react-dom: private
  react-image-crop@11.0.10(react@19.1.0):
    react-image-crop: private
  react-is@19.1.0:
    react-is: private
  react-redux@9.2.0(@types/react@19.1.8)(react@19.1.0)(redux@5.0.1):
    react-redux: private
  react-refresh@0.17.0:
    react-refresh: private
  react-remove-scroll-bar@2.3.8(@types/react@19.1.8)(react@19.1.0):
    react-remove-scroll-bar: private
  react-remove-scroll@2.7.1(@types/react@19.1.8)(react@19.1.0):
    react-remove-scroll: private
  react-style-singleton@2.2.3(@types/react@19.1.8)(react@19.1.0):
    react-style-singleton: private
  react-use-measure@2.1.7(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    react-use-measure: private
  react@19.1.0:
    react: private
  read-cmd-shim@5.0.0:
    read-cmd-shim: private
  readable-stream@4.7.0:
    readable-stream: private
  readdirp@4.1.2:
    readdirp: private
  recharts@3.1.0(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react-is@19.1.0)(react@19.1.0)(redux@5.0.1):
    recharts: private
  redux-thunk@3.1.0(redux@5.0.1):
    redux-thunk: private
  redux@5.0.1:
    redux: private
  reflect-metadata@0.1.13:
    reflect-metadata: private
  registry-auth-token@3.3.2:
    registry-auth-token: private
  registry-url@3.1.0:
    registry-url: private
  replicate@1.0.1:
    replicate: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  require-in-the-middle@7.5.2:
    require-in-the-middle: private
  reselect@5.1.1:
    reselect: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve@1.22.8:
    resolve: private
  restore-cursor@3.1.0:
    restore-cursor: private
  retry-request@8.0.0:
    retry-request: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  rollup-obfuscator@4.1.1(javascript-obfuscator@4.1.1)(rollup@4.44.1):
    rollup-obfuscator: private
  rollup@4.44.1:
    rollup: private
  run-async@2.4.1:
    run-async: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  satori@0.15.2:
    satori: private
  scheduler@0.23.2:
    scheduler: private
  schema-utils@4.3.2:
    schema-utils: private
  semver@7.7.2:
    semver: private
  sentence-case@2.1.1:
    sentence-case: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  set-function-length@1.2.2:
    set-function-length: private
  shared:
    '@rocketsourcing/shared': private
  sharp@0.34.2:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shimmer@1.2.1:
    shimmer: private
  signal-exit@3.0.7:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  slice-ansi@4.0.0:
    slice-ansi: private
  smart-buffer@4.2.0:
    smart-buffer: private
  snake-case@2.1.0:
    snake-case: private
  socks-proxy-agent@8.0.5:
    socks-proxy-agent: private
  socks@2.8.5:
    socks: private
  sonner@2.0.6(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    sonner: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.5.7:
    source-map: private
  sprintf-js@1.1.3:
    sprintf-js: private
  stacktrace-parser@0.1.11:
    stacktrace-parser: private
  stream-events@1.0.5:
    stream-events: private
  stream-shift@1.0.3:
    stream-shift: private
  streamsearch@1.1.0:
    streamsearch: private
  string-template@1.0.0:
    string-template: private
  string-width@4.2.3:
    string-width: private
  string.prototype.codepointat@0.2.1:
    string.prototype.codepointat: private
  string_decoder@1.3.0:
    string_decoder: private
  stringz@2.1.0:
    stringz: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@2.0.1:
    strip-json-comments: private
  strnum@2.1.1:
    strnum: private
  stubs@3.0.0:
    stubs: private
  styled-jsx@5.1.6(react@19.1.0):
    styled-jsx: private
  stylis@4.2.0:
    stylis: private
  supabase@2.30.4:
    supabase: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  swap-case@1.1.2:
    swap-case: private
  table@6.9.0:
    table: private
  tapable@2.2.2:
    tapable: private
  tar@7.4.3:
    tar: private
  teeny-request@10.1.0:
    teeny-request: private
  terser-webpack-plugin@5.3.14(webpack@5.101.3):
    terser-webpack-plugin: private
  terser@5.43.1:
    terser: private
  text-segmentation@1.0.3:
    text-segmentation: private
  through@2.3.8:
    through: private
  tiny-inflate@1.0.3:
    tiny-inflate: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tinycolor2@1.4.2:
    tinycolor2: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tinygradient@1.1.5:
    tinygradient: private
  title-case@2.1.1:
    title-case: private
  tmp@0.0.33:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  tr46@0.0.3:
    tr46: private
  ts-evaluator@1.2.0(typescript@5.8.3):
    ts-evaluator: private
  ts-morph@24.0.0:
    ts-morph: private
  ts-node@10.9.2(@types/node@24.0.4)(typescript@5.8.3):
    ts-node: private
  ts-pattern@5.0.8:
    ts-pattern: private
  tsconfck@3.0.2(typescript@5.8.3):
    tsconfck: private
  tslib@2.8.1:
    tslib: private
  turbo-darwin-64@2.5.4:
    turbo-darwin-64: private
  turbo-darwin-arm64@2.5.4:
    turbo-darwin-arm64: private
  turbo-linux-64@2.5.4:
    turbo-linux-64: private
  turbo-linux-arm64@2.5.4:
    turbo-linux-arm64: private
  turbo-windows-64@2.5.4:
    turbo-windows-64: private
  turbo-windows-arm64@2.5.4:
    turbo-windows-arm64: private
  type-check@0.3.2:
    type-check: private
  type-fest@0.7.1:
    type-fest: private
  typescript@5.8.3:
    typescript: private
  ufo@1.6.1:
    ufo: private
  uglify-js@3.19.3:
    uglify-js: private
  undici-types@7.8.0:
    undici-types: private
  unicode-trie@2.0.0:
    unicode-trie: private
  universalify@2.0.1:
    universalify: private
  unplugin@1.0.1:
    unplugin: private
  update-browserslist-db@1.1.3(browserslist@4.23.3):
    update-browserslist-db: private
  update-check@1.5.4:
    update-check: private
  upper-case-first@1.1.2:
    upper-case-first: private
  upper-case@1.1.3:
    upper-case: private
  use-callback-ref@1.3.3(@types/react@19.1.8)(react@19.1.0):
    use-callback-ref: private
  use-debounce@10.0.5(react@19.1.0):
    use-debounce: private
  use-sidecar@1.1.3(@types/react@19.1.8)(react@19.1.0):
    use-sidecar: private
  use-sync-external-store@1.5.0(react@19.1.0):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  util@0.12.5:
    util: private
  utrie@1.0.2:
    utrie: private
  uuid@9.0.1:
    uuid: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  validate-npm-package-name@5.0.1:
    validate-npm-package-name: private
  validator@13.15.15:
    validator: private
  victory-vendor@37.3.6:
    victory-vendor: private
  vite-plugin-remove-console@2.2.0:
    vite-plugin-remove-console: private
  vite@5.4.19(@types/node@24.0.4)(lightningcss@1.30.1)(terser@5.43.1):
    vite: private
  watchpack@2.4.4:
    watchpack: private
  wcwidth@1.0.1:
    wcwidth: private
  web-streams-polyfill@4.0.0-beta.3:
    web-streams-polyfill: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  webpack-sources@3.3.3:
    webpack-sources: private
  webpack-virtual-modules@0.5.0:
    webpack-virtual-modules: private
  webpack@5.101.3:
    webpack: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wordwrap@1.0.0:
    wordwrap: private
  wordwrapjs@5.1.0:
    wordwrapjs: private
  wrap-ansi@6.2.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@6.0.0:
    write-file-atomic: private
  ws@8.18.3:
    ws: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yallist@5.0.0:
    yallist: private
  yaml@1.10.2:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
  yoga-wasm-web@0.3.3:
    yoga-wasm-web: private
  zod-to-json-schema@3.24.6(zod@4.0.15):
    zod-to-json-schema: private
  zod@4.0.15:
    zod: private
  zustand@5.0.6(@types/react@19.1.8)(immer@10.1.1)(react@19.1.0)(use-sync-external-store@1.5.0(react@19.1.0)):
    zustand: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.15.4
pendingBuilds: []
prunedAt: Mon, 08 Sep 2025 13:41:32 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.4.4'
  - '@emnapi/runtime@1.4.4'
  - '@emnapi/wasi-threads@1.0.3'
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/aix-ppc64@0.25.6'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm64@0.25.6'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-arm@0.25.6'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/android-x64@0.25.6'
  - '@esbuild/darwin-arm64@0.21.5'
  - '@esbuild/darwin-arm64@0.25.6'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/darwin-x64@0.25.6'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-arm64@0.25.6'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/freebsd-x64@0.25.6'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm64@0.25.6'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-arm@0.25.6'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-ia32@0.25.6'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-loong64@0.25.6'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-mips64el@0.25.6'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-ppc64@0.25.6'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-riscv64@0.25.6'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-s390x@0.25.6'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/linux-x64@0.25.6'
  - '@esbuild/netbsd-arm64@0.25.6'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.25.6'
  - '@esbuild/openbsd-arm64@0.25.6'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.25.6'
  - '@esbuild/openharmony-arm64@0.25.6'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/sunos-x64@0.25.6'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-arm64@0.25.6'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-ia32@0.25.6'
  - '@img/sharp-darwin-arm64@0.34.2'
  - '@img/sharp-darwin-x64@0.34.2'
  - '@img/sharp-libvips-darwin-arm64@1.1.0'
  - '@img/sharp-libvips-darwin-x64@1.1.0'
  - '@img/sharp-libvips-linux-arm64@1.1.0'
  - '@img/sharp-libvips-linux-arm@1.1.0'
  - '@img/sharp-libvips-linux-ppc64@1.1.0'
  - '@img/sharp-libvips-linux-s390x@1.1.0'
  - '@img/sharp-libvips-linux-x64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.1.0'
  - '@img/sharp-linux-arm64@0.34.2'
  - '@img/sharp-linux-arm@0.34.2'
  - '@img/sharp-linux-s390x@0.34.2'
  - '@img/sharp-linux-x64@0.34.2'
  - '@img/sharp-linuxmusl-arm64@0.34.2'
  - '@img/sharp-linuxmusl-x64@0.34.2'
  - '@img/sharp-wasm32@0.34.2'
  - '@img/sharp-win32-arm64@0.34.2'
  - '@img/sharp-win32-ia32@0.34.2'
  - '@napi-rs/image-android-arm64@1.11.0'
  - '@napi-rs/image-darwin-arm64@1.11.0'
  - '@napi-rs/image-darwin-x64@1.11.0'
  - '@napi-rs/image-freebsd-x64@1.11.0'
  - '@napi-rs/image-linux-arm-gnueabihf@1.11.0'
  - '@napi-rs/image-linux-arm64-gnu@1.11.0'
  - '@napi-rs/image-linux-arm64-musl@1.11.0'
  - '@napi-rs/image-linux-x64-gnu@1.11.0'
  - '@napi-rs/image-linux-x64-musl@1.11.0'
  - '@napi-rs/image-wasm32-wasi@1.11.0'
  - '@napi-rs/image-win32-arm64-msvc@1.11.0'
  - '@napi-rs/image-win32-ia32-msvc@1.11.0'
  - '@napi-rs/wasm-runtime@0.2.11'
  - '@next/swc-darwin-arm64@15.3.3'
  - '@next/swc-darwin-x64@15.3.3'
  - '@next/swc-linux-arm64-gnu@15.3.3'
  - '@next/swc-linux-arm64-musl@15.3.3'
  - '@next/swc-linux-x64-gnu@15.3.3'
  - '@next/swc-linux-x64-musl@15.3.3'
  - '@next/swc-win32-arm64-msvc@15.3.3'
  - '@rollup/rollup-android-arm-eabi@4.44.1'
  - '@rollup/rollup-android-arm64@4.44.1'
  - '@rollup/rollup-darwin-arm64@4.44.1'
  - '@rollup/rollup-darwin-x64@4.44.1'
  - '@rollup/rollup-freebsd-arm64@4.44.1'
  - '@rollup/rollup-freebsd-x64@4.44.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.1'
  - '@rollup/rollup-linux-arm64-gnu@4.44.1'
  - '@rollup/rollup-linux-arm64-musl@4.44.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.1'
  - '@rollup/rollup-linux-riscv64-musl@4.44.1'
  - '@rollup/rollup-linux-s390x-gnu@4.44.1'
  - '@rollup/rollup-linux-x64-gnu@4.44.1'
  - '@rollup/rollup-linux-x64-musl@4.44.1'
  - '@rollup/rollup-win32-arm64-msvc@4.44.1'
  - '@rollup/rollup-win32-ia32-msvc@4.44.1'
  - '@sentry/cli-darwin@2.52.0'
  - '@sentry/cli-linux-arm64@2.52.0'
  - '@sentry/cli-linux-arm@2.52.0'
  - '@sentry/cli-linux-i686@2.52.0'
  - '@sentry/cli-linux-x64@2.52.0'
  - '@sentry/cli-win32-arm64@2.52.0'
  - '@sentry/cli-win32-i686@2.52.0'
  - '@tybys/wasm-util@0.9.0'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.25.1
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.25.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.25.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.25.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.25.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.25.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.25.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.25.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - turbo-darwin-64@2.5.4
  - turbo-darwin-arm64@2.5.4
  - turbo-linux-64@2.5.4
  - turbo-linux-arm64@2.5.4
  - turbo-windows-arm64@2.5.4
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v3
virtualStoreDir: C:\Users\<USER>\Desktop\workspace\Rocketsourcingv2\node_modules\.pnpm
virtualStoreDirMaxLength: 120
