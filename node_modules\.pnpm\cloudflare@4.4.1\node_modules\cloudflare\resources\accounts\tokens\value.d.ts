import { APIResource } from "../../../resource.js";
import * as Core from "../../../core.js";
import * as Shared from "../../shared.js";
export declare class Value extends APIResource {
    /**
     * Roll the Account Owned API token secret.
     *
     * @example
     * ```ts
     * const tokenValue =
     *   await client.accounts.tokens.value.update(
     *     'ed17574386854bf78a67040be0a770b0',
     *     {
     *       account_id: '023e105f4ecef8ad9ca31a8372d0c353',
     *       body: {},
     *     },
     *   );
     * ```
     */
    update(tokenId: string, params: ValueUpdateParams, options?: Core.RequestOptions): Core.APIPromise<Shared.TokenValue>;
}
export interface ValueUpdateParams {
    /**
     * Path param: Account identifier tag.
     */
    account_id: string;
    /**
     * Body param:
     */
    body: unknown;
}
export declare namespace Value {
    export { type ValueUpdateParams as ValueUpdateParams };
}
//# sourceMappingURL=value.d.ts.map