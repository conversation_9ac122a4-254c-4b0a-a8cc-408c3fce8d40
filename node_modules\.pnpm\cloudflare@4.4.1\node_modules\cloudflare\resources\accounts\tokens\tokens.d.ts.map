{"version": 3, "file": "tokens.d.ts", "sourceRoot": "", "sources": ["../../../src/resources/accounts/tokens/tokens.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,KAAK,IAAI,MAAM,eAAe,CAAC;AACtC,OAAO,KAAK,MAAM,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,2BAA2B,EAAE,MAAM,cAAc,CAAC;AAC3D,OAAO,KAAK,mBAAmB,MAAM,qBAAqB,CAAC;AAC3D,OAAO,EACL,wBAAwB,EACxB,0BAA0B,EAC1B,qCAAqC,EACrC,yBAAyB,EACzB,2BAA2B,EAC3B,sCAAsC,EACtC,gBAAgB,EACjB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,KAAK,QAAQ,MAAM,SAAS,CAAC;AACpC,OAAO,EAAE,KAAK,EAAE,iBAAiB,EAAE,MAAM,SAAS,CAAC;AACnD,OAAO,EAAE,KAAK,2BAA2B,EAAE,MAAM,qBAAqB,CAAC;AAEvE,qBAAa,MAAO,SAAQ,WAAW;IACrC,gBAAgB,EAAE,mBAAmB,CAAC,gBAAgB,CAEpD;IACF,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAoC;IAEzD;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACH,MAAM,CAAC,MAAM,EAAE,iBAAiB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC;IAStG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6BG;IACH,MAAM,CACJ,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,iBAAiB,EACzB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC;IAShC;;;;;;;;;;;;OAYG;IACH,IAAI,CACF,MAAM,EAAE,eAAe,EACvB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,WAAW,CAAC,2BAA2B,EAAE,MAAM,CAAC,KAAK,CAAC;IAQ9D;;;;;;;;;;OAUG;IACH,MAAM,CACJ,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,iBAAiB,EACzB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,UAAU,CAAC,mBAAmB,GAAG,IAAI,CAAC;IAS9C;;;;;;;;;;OAUG;IACH,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC;IAS1G;;;;;;;;;OASG;IACH,MAAM,CAAC,MAAM,EAAE,iBAAiB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC;CAQvG;AAED,MAAM,WAAW,mBAAmB;IAClC;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IAEZ,SAAS,CAAC,EAAE,mBAAmB,CAAC,SAAS,CAAC;IAE1C;;;OAGG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,QAAQ,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IAErC;;OAEG;IACH,MAAM,CAAC,EAAE,QAAQ,GAAG,UAAU,GAAG,SAAS,CAAC;IAE3C;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC,UAAU,CAAC;CAC3B;AAED,yBAAiB,mBAAmB,CAAC;IACnC,UAAiB,SAAS;QACxB;;WAEG;QACH,UAAU,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC;KAClC;IAED,UAAiB,SAAS,CAAC;QACzB;;WAEG;QACH,UAAiB,SAAS;YACxB;;eAEG;YACH,EAAE,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;YAE1C;;eAEG;YACH,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;SAC/C;KACF;CACF;AAED,MAAM,WAAW,mBAAmB;IAClC;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;CACZ;AAED,MAAM,WAAW,mBAAmB;IAClC;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,MAAM,EAAE,QAAQ,GAAG,UAAU,GAAG,SAAS,CAAC;IAE1C;;;OAGG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IAEzC;;OAEG;IACH,SAAS,CAAC,EAAE,iBAAiB,CAAC,SAAS,CAAC;IAExC;;;OAGG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED,yBAAiB,iBAAiB,CAAC;IACjC,UAAiB,SAAS;QACxB;;WAEG;QACH,UAAU,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC;KAClC;IAED,UAAiB,SAAS,CAAC;QACzB;;WAEG;QACH,UAAiB,SAAS;YACxB;;eAEG;YACH,EAAE,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAC;YAE/C;;eAEG;YACH,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAC;SACpD;KACF;CACF;AAED,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IAEzC;;OAEG;IACH,MAAM,EAAE,QAAQ,GAAG,UAAU,GAAG,SAAS,CAAC;IAE1C;;OAEG;IACH,SAAS,CAAC,EAAE,iBAAiB,CAAC,SAAS,CAAC;IAExC;;;OAGG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED,yBAAiB,iBAAiB,CAAC;IACjC,UAAiB,SAAS;QACxB;;WAEG;QACH,UAAU,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC;KAClC;IAED,UAAiB,SAAS,CAAC;QACzB;;WAEG;QACH,UAAiB,SAAS;YACxB;;eAEG;YACH,EAAE,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAC;YAE/C;;eAEG;YACH,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAC;SACpD;KACF;CACF;AAED,MAAM,WAAW,eAAgB,SAAQ,2BAA2B;IAClE;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,SAAS,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC;CAC5B;AAED,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,cAAc;IAC7B;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;CACpB;AAOD,MAAM,CAAC,OAAO,WAAW,MAAM,CAAC;IAC9B,OAAO,EACL,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,eAAe,IAAI,eAAe,EACvC,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,cAAc,IAAI,cAAc,EACrC,KAAK,iBAAiB,IAAI,iBAAiB,GAC5C,CAAC;IAEF,OAAO,EACL,gBAAgB,IAAI,gBAAgB,EACpC,KAAK,2BAA2B,IAAI,2BAA2B,EAC/D,KAAK,0BAA0B,IAAI,0BAA0B,EAC7D,sCAAsC,IAAI,sCAAsC,EAChF,qCAAqC,IAAI,qCAAqC,EAC9E,KAAK,yBAAyB,IAAI,yBAAyB,EAC3D,KAAK,wBAAwB,IAAI,wBAAwB,GAC1D,CAAC;IAEF,OAAO,EAAE,KAAK,IAAI,KAAK,EAAE,KAAK,iBAAiB,IAAI,iBAAiB,EAAE,CAAC;CACxE;AAED,OAAO,EAAE,2BAA2B,EAAE,CAAC"}