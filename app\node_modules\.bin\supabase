#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/workspace/Rocketsourcingv2/node_modules/.pnpm/supabase@2.30.4/node_modules/supabase/bin/node_modules:/mnt/c/Users/<USER>/Desktop/workspace/Rocketsourcingv2/node_modules/.pnpm/supabase@2.30.4/node_modules/supabase/node_modules:/mnt/c/Users/<USER>/Desktop/workspace/Rocketsourcingv2/node_modules/.pnpm/supabase@2.30.4/node_modules:/mnt/c/Users/<USER>/Desktop/workspace/Rocketsourcingv2/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/workspace/Rocketsourcingv2/node_modules/.pnpm/supabase@2.30.4/node_modules/supabase/bin/node_modules:/mnt/c/Users/<USER>/Desktop/workspace/Rocketsourcingv2/node_modules/.pnpm/supabase@2.30.4/node_modules/supabase/node_modules:/mnt/c/Users/<USER>/Desktop/workspace/Rocketsourcingv2/node_modules/.pnpm/supabase@2.30.4/node_modules:/mnt/c/Users/<USER>/Desktop/workspace/Rocketsourcingv2/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
"$basedir/../supabase/bin/supabase"   "$@"
exit $?
