// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../../resource.mjs";
import * as AuditAPI from "./audit.mjs";
import { Audit, AuditListResponsesCursorLimitPagination } from "./audit.mjs";
export class Logs extends APIResource {
    constructor() {
        super(...arguments);
        this.audit = new AuditAPI.Audit(this._client);
    }
}
Logs.Audit = Audit;
Logs.AuditListResponsesCursorLimitPagination = AuditListResponsesCursorLimitPagination;
//# sourceMappingURL=logs.mjs.map