"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Value = exports.Tokens = exports.PermissionGroups = exports.PermissionGroupGetResponsesSinglePage = exports.PermissionGroupListResponsesSinglePage = void 0;
var permission_groups_1 = require("./permission-groups.js");
Object.defineProperty(exports, "PermissionGroupListResponsesSinglePage", { enumerable: true, get: function () { return permission_groups_1.PermissionGroupListResponsesSinglePage; } });
Object.defineProperty(exports, "PermissionGroupGetResponsesSinglePage", { enumerable: true, get: function () { return permission_groups_1.PermissionGroupGetResponsesSinglePage; } });
Object.defineProperty(exports, "PermissionGroups", { enumerable: true, get: function () { return permission_groups_1.PermissionGroups; } });
var tokens_1 = require("./tokens.js");
Object.defineProperty(exports, "Tokens", { enumerable: true, get: function () { return tokens_1.Tokens; } });
var value_1 = require("./value.js");
Object.defineProperty(exports, "Value", { enumerable: true, get: function () { return value_1.Value; } });
//# sourceMappingURL=index.js.map